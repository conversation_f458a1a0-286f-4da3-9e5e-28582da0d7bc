"use client";

import React, { useState, useEffect, useRef } from 'react';
import { FiDownload, FiCopy, FiUpload, FiGrid, FiX, FiSearch, FiChevronDown } from "react-icons/fi";
import {PaperClipIcon, ArrowUpIcon } from '@heroicons/react/24/outline';
import { toast } from "sonner";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useSubscription } from "@/contexts/SubscriptionContext";
import { getAllModels, modelsByCategory, supportsImageInput, requiresOpenAIKey, supportsMultipleImages} from "@/lib/models-data";
import { GeneratedImage } from "@/lib/image-utils";
import GeneratedImagesGrid from "@/components/image/GeneratedImagesGrid";
import DownloadModal from "@/components/image/DownloadModal";
import ReplicateImagesGrid from "@/components/image/ReplicateImagesGrid";
import Image from 'next/image';
import { AnimatePresence, motion } from 'framer-motion';
import { Message, createMessage, createTextMessage, createImageGridMessage, createImageUploadMessage, serializeMessages, deserializeMessages, SerializableMessage, shouldReconstructMessage } from '@/lib/message-utils';
import MessageRenderer from '@/components/MessageRenderer';
import { useImageGenerationTranslations, useCommonTranslations } from '@/hooks/useTranslations';


// Helper function to get user-friendly category descriptions
const getCategoryDescription = (category: string): string => {
  switch (category) {
    case "Quick Generation":
      return "Fast image creation";
    case "Photorealistic":
      return "Realistic image quality";
    case "Artistic":
      return "Creative and stylized";
    case "Specialized":
      return "Domain-specific models";
    case "Image Editing":
      return "Edit existing images";
    case "Upscaling Models":
      return "Enhance resolution";
    default:
      return category;
  }
};

// Custom CSS for modern UI
const customStyles = `
  /* Hide scrollbar for clean UI */
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  .hide-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  /* Custom scrollbar for model selection */
  .custom-scrollbar::-webkit-scrollbar {
    width: 5px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-track {
    background: #1a1a1a;
    border-radius: 10px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: #333;
    border-radius: 10px;
  }
  
  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #444;
  }
  
  /* Animation for modal */
  @keyframes modalFadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }
  
  .modal-animation {
    animation: modalFadeIn 0.2s ease-out;
  }

  /* Format button hover effects */
  .format-btn:hover {
    background-color: #222;
  }
  
  .format-btn.selected {
    background-color: white;
    color: black;
  }
  
  /* Image preview animation */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
  
  .image-animation {
    animation: fadeIn 0.5s ease-out;
  }
  
  /* Loading pulsing animation */
  @keyframes pulse {
    0% { opacity: 0.6; }
    50% { opacity: 1; }
    100% { opacity: 0.6; }
  }
  
  .loading-pulse {
    animation: pulse 1.5s infinite ease-in-out;
  }

  /* Progress bar animation */
  @keyframes progress {
    0% { width: 0%; }
    100% { width: 100%; }
  }

  .animate-progress {
    animation: progress 2s linear infinite;
  }

  /* Checkerboard background for transparent images */
  .bg-checkerboard {
    background-image: linear-gradient(45deg, #111 25%, transparent 25%), 
                      linear-gradient(-45deg, #111 25%, transparent 25%), 
                      linear-gradient(45deg, transparent 75%, #111 75%), 
                      linear-gradient(-45deg, transparent 75%, #111 75%);
    background-size: 20px 20px;
    background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
    background-color: #1a1a1a;
  }
  
  /* Message bubble styles */
  .message-bubble-user {
    background-color: #1e1e1e;
    border-radius: 18px 18px 0 18px;
    color: white;
    padding: 12px 16px;
  }
  
  .message-bubble-ai {
    background-color: #212121;
    border-radius: 18px 18px 18px 0;
    color: #417ef7;
    padding: 12px 16px;
  }
  
  /* Model selector styles */
  .model-selector {
    background-color: #1a1a1a;
    border: 1px solid #333;
    border-radius: 8px;
    padding: 8px 12px;
    transition: all 0.2s ease;
  }
  
  .model-selector:hover {
    background-color: #222;
    border-color: #444;
  }
  
  .model-dropdown {
    background-color: #1a1a1a;
    border: 1px solid #333;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  }
  
  .model-option {
    padding: 8px 12px;
    transition: all 0.2s ease;
  }
  
  .model-option:hover {
    background-color: #222;
  }
  
  .model-option.selected {
    background-color: #333;
  }
`;

// Define the free tier models
const FREE_TIER_MODELS = ["black-forest-labs/FLUX.1-schnell-Free"];

// FreeTierContent component for users without subscription
const FreeTierContent = () => {
  const router = useRouter();
  const [mode, setMode] = useState<GenerationMode>('image-generation');
  const [prompt, setPrompt] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);

  // Translation hooks
  const t = useImageGenerationTranslations();
  const common = useCommonTranslations();
  const [generatedImages, setGeneratedImages] = useState<GeneratedImage[]>([]);
  const [selectedImage, setSelectedImage] = useState<GeneratedImage | null>(null);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  // Initialize messages as empty array to prevent hydration mismatch
  const [messages, setMessages] = useState<Message[]>([]);
  const [isMessagesLoaded, setIsMessagesLoaded] = useState(false);

  // Load messages from localStorage after component mounts (client-side only)
  useEffect(() => {
    try {
      const savedMessages = localStorage.getItem('astrostudio-image-generation-messages');
      if (savedMessages) {
        const serializedMessages = deserializeMessages(savedMessages);
        // Convert serialized messages back to Message objects with placeholder content
        const loadedMessages = serializedMessages.map(serialized => ({
          ...serialized,
          timestamp: new Date(serialized.timestamp),
          content: <div>Loading message content...</div> // Placeholder content
        }));
        setMessages(loadedMessages);
      }
    } catch (error) {
      console.error('Error loading messages from localStorage:', error);
    } finally {
      setIsMessagesLoaded(true);
    }
  }, []);
  const [isModelSelectorOpen, setIsModelSelectorOpen] = useState<boolean>(false);
  const [modelSearchQuery, setModelSearchQuery] = useState<string>('');
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "success" | "error">("idle");
  const [isDimensionSelectorOpen, setIsDimensionSelectorOpen] = useState<boolean>(false);
  const [showDownloadModal, setShowDownloadModal] = useState<boolean>(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Para acceder a la información de suscripción
  const { subscription } = useSubscription();

  // Refs for auto-scrolling and textarea height adjustment
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const messageContainerRef = useRef<HTMLDivElement>(null);

  // Save messages to localStorage whenever they change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const serializedMessages = serializeMessages(messages);
        localStorage.setItem('astrostudio-image-generation-messages', serializedMessages);
      } catch (error) {
        console.error('Error saving messages to localStorage:', error);
      }
    }
  }, [messages]);

  // Limited settings for free tier
  const defaultModelId = "black-forest-labs/FLUX.1-schnell-Free";
  const [selectedModel, setSelectedModel] = useState<string>(defaultModelId);
  const [negativePrompt, setNegativePrompt] = useState<string>("");
  const [numImages, setNumImages] = useState<number>(1);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState<boolean>(false);

  // Dimension state - limited in free tier (only basic dimensions)
  const freeTierDimensions: Dimension[] = [
    { id: "1024x1024", label: "1:1 Square", width: 1024, height: 1024 },
    { id: "1024x768", label: "4:3 Landscape", width: 1024, height: 768 },
    { id: "768x1024", label: "3:4 Portrait", width: 768, height: 1024 },
  ];
  const [selectedDimension, setSelectedDimension] = useState<string>("1024x1024");

  // Get all models for search
  const allModels = getAllModels();

  // Filter models based on search query
  const filteredModels = modelSearchQuery
    ? allModels.filter(model =>
      model.name.toLowerCase().includes(modelSearchQuery.toLowerCase()) ||
      model.provider.toLowerCase().includes(modelSearchQuery.toLowerCase())
    )
    : allModels;

  // Auto-scroll to the bottom when new messages are added
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Get provider of the selected model
  const getSelectedModelProvider = (): string => {
    // Default provider detection based on model ID
    if (selectedModel.includes('together-ai') || selectedModel.startsWith('black-forest-labs/FLUX')) {
      return "Together AI";
    }
    return "Replicate";
  };

  // Determine current provider
  const currentProvider = getSelectedModelProvider();
  const isReplicateProvider = currentProvider === "Replicate";

  // Handle mode change - limited options in free tier
  const handleModeChange = (newMode: GenerationMode) => {
    if (newMode === mode) return;

    // If switching to image-editing or upscale-image, show subscription prompt
    if (newMode === 'image-editing' || newMode === 'upscale-image') {
      toast.info(
        <div className="flex flex-col gap-1">
          <span className="font-semibold">{t('subscriptionRequired')}</span>
          <span className="text-sm">{t('subscriptionRequiredDescription')}</span>
          <Link href="/dashboard/subscription" className="text-xs text-gray-300 underline mt-1">
            {t('viewSubscriptionPlans')}
          </Link>
        </div>
      );
      return;
    }

    setMode(newMode);
  };

  // Get width and height values
  const getDimensions = () => {
    const [width, height] = selectedDimension.split("x").map(Number);
    return { width, height };
  };

  // Get currently selected model
  const getCurrentModel = () => {
    return allModels.find(model => model.id === selectedModel);
  };

  // Toggle model selector
  const toggleModelSelector = () => {
    setIsModelSelectorOpen(!isModelSelectorOpen);
    // Reset search when opening
    if (!isModelSelectorOpen) {
      setModelSearchQuery('');
    }
  };

  // Select a model
  const selectModel = (modelId: string) => {
    setSelectedModel(modelId);
    setIsModelSelectorOpen(false);
    inputRef.current?.focus();
  };

  // Adjust textarea height based on content
  const adjustTextareaHeight = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const target = e.target;
    target.style.height = 'inherit';
    target.style.height = `${Math.min(target.scrollHeight, 150)}px`;
  };

  // Handle textarea resize and enter key
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleGenerate();
    }
  };

  // Handle image generation
  const handleGenerate = async () => {
    if (!prompt.trim() && !uploadedImage) {
      toast.error(t('errors.promptRequired'));
      return;
    }

    try {
      setError("");
      setIsGenerating(true);

      const { width, height } = getDimensions();
      const provider = getSelectedModelProvider();
      const isReplicateModel = provider === "Replicate";

      // Add user message
      setMessages(prev => [
        ...prev,
        createTextMessage(
          prompt,
          true,
          undefined,
          selectedModel,
          uploadedImage || undefined
        )
      ]);

      // Prepare request data
      const requestData: any = {
        model: selectedModel,
        provider,
        prompt: prompt.trim(),
        negative_prompt: negativePrompt.trim(),
        width,
        height,
        num_images: numImages,
        feature: "IMAGE_GENERATION",
        image: uploadedImage ?? undefined
      };
      
      // Add input_images for models that support image input
      if (uploadedImage && supportsImageInput(selectedModel)) {
        requestData.input_images = [uploadedImage];
      }
      const openaiApiKey = process.env.OPENAI_API_KEY;
      // Add OpenAI API key for GPT image model
      if (requiresOpenAIKey(selectedModel)) {
        requestData.openai_api_key = openaiApiKey ?? undefined;
      }

      // Make API request
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error ?? 'Failed to generate image');
      }

      const data = await response.json();

      // DEBUG: Log the complete API response for debugging
      console.log('🔍 [DEBUG] Complete API response:', JSON.stringify(data, null, 2));

      // Enhanced Replicate response handling
      let finalImages: string[] = [];
      if (isReplicateModel) {
        console.log('🔍 [DEBUG] Processing Replicate response...');
        console.log('🔍 [DEBUG] data.images:', data.images);
        console.log('🔍 [DEBUG] data.raw_response:', data.raw_response);

        // First try the standard images array
        if (data.images && Array.isArray(data.images) && data.images.length > 0) {
          finalImages = data.images.filter((url: any) => typeof url === 'string' && url.trim().length > 0);
          console.log('🔍 [DEBUG] Found images in data.images:', finalImages);
        }

        // If no images found, try to extract from raw_response
        if (finalImages.length === 0 && data.raw_response) {
          console.log('🔍 [DEBUG] No images in data.images, checking raw_response...');
          const rawResponse = data.raw_response;

          // Try different possible paths in the raw response
          const possiblePaths = ['output', 'images', 'data', 'urls', 'image_urls'];
          for (const path of possiblePaths) {
            if (rawResponse[path]) {
              console.log(`🔍 [DEBUG] Found ${path} in raw_response:`, rawResponse[path]);
              if (Array.isArray(rawResponse[path])) {
                const validUrls = rawResponse[path].filter(url => typeof url === 'string' && url.trim().length > 0);
                if (validUrls.length > 0) {
                  finalImages = validUrls;
                  console.log(`🔍 [DEBUG] Extracted images from ${path}:`, finalImages);
                  break;
                }
              } else if (typeof rawResponse[path] === 'string' && rawResponse[path].trim().length > 0) {
                finalImages = [rawResponse[path]];
                console.log(`🔍 [DEBUG] Extracted single image from ${path}:`, finalImages);
                break;
              }
            }
          }
        }

        // Update data.images with the extracted images
        data.images = finalImages;
        console.log('🔍 [DEBUG] Final extracted images for Replicate:', finalImages);
      } else {
        // For non-Replicate providers, use images as-is
        finalImages = data.images ?? [];
        console.log('🔍 [DEBUG] Using images as-is for non-Replicate provider:', finalImages);
      }

      // Create new generated images
      const newImages: GeneratedImage[] = finalImages.map((url: string, index: number) => ({
        id: Date.now() + index,
        url,
        prompt,
        negativePrompt,
        model: selectedModel,
        width,
        height,
        timestamp: new Date(),
        provider,
        raw_response: isReplicateModel ? data.raw_response : null
      }));

      console.log('🔍 [DEBUG] Created newImages array:', newImages);
      console.log('🔍 [DEBUG] newImages.length:', newImages.length);

      if (newImages.length === 0) {
        console.error('❌ [ERROR] No images generated - this should not happen if API returned images');
        throw new Error(t('errors.generationFailed'));
      }

      setGeneratedImages(prev => [...newImages, ...prev]);
      setSelectedImage(newImages[0]);

      // Add AI message with generated images
      console.log('🔍 [DEBUG] Creating image grid message with:', {
        newImages,
        provider,
        selectedModel,
        numImages,
        isUser: false
      });

      const imageGridMessage = createImageGridMessage(
        newImages,
        provider,
        selectedModel,
        numImages,
        false
      );

      console.log('🔍 [DEBUG] Created image grid message:', imageGridMessage);
      console.log('🔍 [DEBUG] Message data:', imageGridMessage.data);

      setMessages(prev => [
        ...prev,
        imageGridMessage
      ]);

      // Clear prompt and upload
      setPrompt("");
      setUploadedImage(null);
      setUploadStatus("idle");
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message || 'Failed to generate image. Please try again.');
        toast.error(err.message || 'Failed to generate image. Please try again.');
      } else {
        setError('Failed to generate image. Please try again.');
        toast.error('Failed to generate image. Please try again.');
      }
    } finally {
      setIsGenerating(false);
    }
  };

  // Save generated image to Supabase
  const saveImageToSupabase = async (imageUrl: string, index: number): Promise<string | null> => {
    try {
      const response = await fetch('/api/user/save-generated-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl,
          prompt,
          model: selectedModel,
          width: getDimensions().width,
          height: getDimensions().height,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save image');
      }

      // Return the image URL on success
      return imageUrl;
    } catch (error) {
      console.error('Error saving image to user history:', error);
      return null;
    }
  };

  // Copy prompt to clipboard
  const copyPrompt = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(common('copied'));
    } catch (err) {
      console.error('Failed to copy text:', err);
      setError("Failed to copy text to clipboard.");
    }
  };

  // Download image
  const downloadImage = async (url: string) => {
    // If the image is selected, open the download modal instead of direct download
    if (selectedImage && selectedImage.url === url) {
      setIsSaving(true);
      return;
    }

    // Find the image in generatedImages if not the selected one
    const imageToDownload = generatedImages.find(img => img.url === url);
    if (imageToDownload) {
      setSelectedImage(imageToDownload);
      setIsSaving(true);
      return;
    }

    // Fallback to direct download if no image found
    try {
      // Use the download-image API to handle CORS issues
      const response = await fetch('/api/download-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ imageUrl: url }),
      });

      if (!response.ok) {
        throw new Error('Failed to fetch image data');
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error ?? 'Failed to download image');
      }

      // Create a temporary link to download the data URL
      const a = document.createElement('a');
      a.href = data.dataUrl;
      a.download = `generated-image-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();

      // Clean up
      document.body.removeChild(a);
    } catch (err) {
      console.error('Error downloading image:', err);
      toast.error('Failed to download image. Please try again.');
    }
  };

  // Handle file upload
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    // Check if the selected model supports image input
    if (!supportsImageInput(selectedModel)) {
      toast.error(t('errors.modelNotSupported'));
      return;
    }

    const isMultipleSupported = supportsMultipleImages(selectedModel);
    const filesToProcess = isMultipleSupported ? Array.from(files) : [files[0]];

    // Validate all files are images and check size
    for (const file of filesToProcess) {
      if (!file.type.startsWith("image/")) {
        toast.error(t('errors.imageFilesOnly'));
        return;
      }
      if (file.size > 10 * 1024 * 1024) {
        toast.error(t('errors.fileSizeLimit'));
        return;
      }
    }

    setUploadStatus("uploading");
    toast.loading(filesToProcess.length > 1 ? t('loading.uploadingImages') : t('loading.uploadingImage'));

    const processFiles = async () => {
      const results: string[] = [];

      for (const file of filesToProcess) {
        const reader = new FileReader();
        const result = await new Promise<string>((resolve, reject) => {
          reader.onload = (event) => {
            if (event.target?.result) {
              resolve(event.target.result as string);
            } else {
              reject(new Error("Failed to read file"));
            }
          };
          reader.onerror = () => reject(new Error("Failed to read file"));
          reader.readAsDataURL(file);
        });
        results.push(result);
      }

      if (isMultipleSupported) {
        setUploadedImages(results);
        setUploadedImage(null);
      } else {
        setUploadedImage(results[0]);
        setUploadedImages([]);
      }

      setUploadStatus("success");
      toast.dismiss();
      toast.success(`${results.length} ${results.length > 1 ? t('success.imagesUploaded') : t('success.imageUploaded')}`);
    };

    processFiles().catch(() => {
      setUploadStatus("error");
      setError(t('errors.failedToReadImageFiles'));
      toast.dismiss();
      toast.error(t('errors.failedToReadImageFiles'));
    });
  };

  // Handle paste
  const handlePaste = (e: React.ClipboardEvent) => {
    const items = e.clipboardData.items;
    for (let i = 0; i < items.length; i++) {
      if (items[i].type.indexOf('image') !== -1) {
        const blob = items[i].getAsFile();
        if (!blob) continue;

        // Check file size
        if (blob.size > 10 * 1024 * 1024) {
          toast.error(t('errors.fileSizeLimit'));
          return;
        }

        setUploadStatus("uploading");
        toast.loading(t('loading.processingPastedImage'));

        const reader = new FileReader();
        reader.onload = (event) => {
          if (event.target?.result) {
            const result = event.target.result as string;
            setUploadedImage(result);
            setUploadStatus("success");
            setError("");
            toast.dismiss();
            toast.success(t('pasteImageSuccess'));

            // Add a message about the uploaded image
            setMessages(prev => [
              ...prev,
              createImageUploadMessage(
                t('pasteImageDescription'),
                result,
                true
              )
            ]);
          }
        };

        reader.onerror = () => {
          setUploadStatus("error");
          setError(t('errors.failedToReadImageFile'));
          toast.dismiss();
          toast.error(t('errors.failedToReadImageFile'));
        };

        reader.readAsDataURL(blob);
        break;
      }
    }
  };

  // Clear uploaded image
  const clearUploadedImage = () => {
    setUploadedImage(null);
    setUploadStatus("idle");
  };

  // Get category description for models
  const getCategoryDescription = (category: string): string => {
    const descriptions: { [key: string]: string } = {
      "Image Generation": "Text to Image",
      "Image Editing": "Edit Images",
      "Upscaling Models": "Enhance Quality",
      "Flux Models": "Advanced Generation"
    };
    return descriptions[category] || category;
  };

  // Render content
  return (
    <div className="h-full flex flex-col">
      {/* Chat Interface with dark styling */}
      <div className="flex-1 min-h-0 overflow-hidden">
        <div className="h-full flex flex-col bg-black rounded-xl">
          <div className="flex items-center justify-between p-3 sm:p-4 border-b border-[#222]">
            <h1 className="text-lg sm:text-xl font-medium text-white truncate pr-2">{t('freeTierTitle')}</h1>
            <div className="flex items-center gap-2">
              <button
                className="flex items-center text-gray-400 hover:text-white text-xs sm:text-sm bg-[#222] hover:bg-[#333] px-2 sm:px-3 py-1.5 rounded-lg min-h-[44px] sm:min-h-auto"
                onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
              >
                <FiGrid className="mr-1 sm:mr-2" />
                <span className="hidden sm:inline">{showAdvancedOptions ? t('hideDimensions') : t('showDimensions')}</span>
                <span className="sm:hidden">Size</span>
              </button>
            </div>
          </div>

          {/* Settings Panel (conditionally shown) */}
          {showAdvancedOptions && (
            <div className="bg-[#111] border-b border-[#222] p-3 sm:p-4">
              <div className="flex flex-col sm:flex-row gap-3 sm:gap-2 sm:justify-between sm:items-center">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-400">Size:</span>
                  <div className="relative flex-1 sm:flex-none">
                    <button
                      onClick={() => setIsDimensionSelectorOpen(!isDimensionSelectorOpen)}
                      className="flex items-center justify-between w-full sm:w-auto px-3 py-2 rounded-lg bg-[#222] hover:bg-[#333] text-gray-300 transition-colors text-sm min-h-[44px] sm:min-h-auto"
                    >
                      <span className="truncate">
                        {freeTierDimensions.find(d => d.id === selectedDimension)?.label ?? selectedDimension}
                      </span>
                      <FiChevronDown className={`ml-2 flex-shrink-0 transition-transform ${isDimensionSelectorOpen ? 'rotate-180' : ''}`} />
                    </button>

                    {/* Dimension selector dropdown */}
                    <AnimatePresence>
                      {isDimensionSelectorOpen && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          exit={{ opacity: 0, y: 10 }}
                          transition={{ duration: 0.2 }}
                          className="absolute left-0 top-full mt-1 z-50 bg-[#111] border border-[#222] rounded-lg overflow-hidden shadow-lg"
                        >
                          <div className="p-2 max-h-[350px] overflow-y-auto custom-scrollbar">
                            {freeTierDimensions.map((dimension) => (
                              <button
                                key={dimension.id}
                                className={`p-3 rounded-lg text-left transition w-full ${selectedDimension === dimension.id
                                    ? "bg-gradient-to-r from-[#141493] to-[#417ef7] text-white"
                                    : "hover:bg-[#222] text-gray-300"
                                  }`}
                                onClick={() => {
                                  setSelectedDimension(dimension.id);
                                  setIsDimensionSelectorOpen(false);
                                }}
                              >
                                <div className="flex justify-between items-center">
                                  <div>
                                    <div className="text-sm font-medium">{dimension.label}</div>
                                    <div className="text-xs text-gray-400">{dimension.width} x {dimension.height}</div>
                                  </div>
                                  <div className="w-5 h-5 rounded-full border-2 border-white flex items-center justify-center">
                                    {selectedDimension === dimension.id && (
                                      <div className="w-3 h-3 bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full"></div>
                                    )}
                                  </div>
                                </div>
                              </button>
                            ))}
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                </div>
                <div className="text-xs text-gray-500 bg-[#222] px-2 py-1 rounded text-center sm:text-left">
                  {t('freeTierLimited')}
                </div>
              </div>
            </div>
          )}

          {/* Chat Interface */}
          <div className="flex flex-col h-full overflow-y-auto">
            {/* Message area */}
            <div className="flex-1 p-3 overflow-y-auto auto-scroll custom-scrollbar">
              {messages.length === 0 && (
                <div className="flex flex-col items-center justify-center h-full text-center text-gray-400 space-y-6 py-10">
                  <div className="flex flex-col items-center">
                    <Image src="/Logo Web.webp" alt="AstroStudio AI" width={200} height={70} className="mb-4" />
                    <p className="text-2xl font-medium text-gray-300 mb-4">{t('aiImageGeneration')}</p>
                    <p className="max-w-lg mx-auto">
                      {t('description')}
                    </p>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full max-w-2xl">
                    <button
                      onClick={() => setPrompt(t('examples.photoRealistic.description'))}
                      className="p-4 bg-[#111] hover:bg-[#222] border border-[#333] rounded-lg text-left transition-colors"
                    >
                      <h3 className="font-medium text-white mb-1">{t('examples.photoRealistic.title')}</h3>
                      <p className="text-sm text-gray-400">{t('examples.photoRealistic.description')}</p>
                    </button>
                    <button
                      onClick={() => setPrompt(t('examples.fantasyLandscape.description'))}
                      className="p-4 bg-[#111] hover:bg-[#222] border border-[#333] rounded-lg text-left transition-colors"
                    >
                      <h3 className="font-medium text-white mb-1">{t('examples.fantasyLandscape.title')}</h3>
                      <p className="text-sm text-gray-400">{t('examples.fantasyLandscape.description')}</p>
                    </button>
                  </div>
                </div>
              )}

              {/* Messages */}
              <div className="space-y-6">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={`flex ${message.isUser ? 'justify-end' : 'justify-start'} max-w-full`}
                  >
                    <div className={`flex ${message.isUser ? 'flex-row-reverse' : 'flex-row'} items-start gap-3 max-w-[90%]`}>
                      <div
                        className={`${message.isUser
                            ? 'message-bubble-user'
                            : 'message-bubble-ai'
                          } max-w-full overflow-hidden`}
                      >
                        <div className="break-words">
                          <MessageRenderer
                            message={message}
                            onSelectImage={setSelectedImage}
                            onDownloadImage={downloadImage}
                            onSaveImage={saveImageToSupabase}
                            isSaving={isSaving}
                            ReplicateImagesGrid={ReplicateImagesGrid}
                            GeneratedImagesGrid={GeneratedImagesGrid}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                {/* Loading indicator */}
                {isGenerating && (
                  <div className="flex justify-start max-w-full">
                    <div className="flex flex-row items-start gap-3 max-w-[90%]">
                      <div className="message-bubble-ai">
                        <div className="flex space-x-2 items-center h-5">
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                          <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              <div ref={messagesEndRef} />
            </div>

            {/* Input area - matching subscription tier design */}
            <div className="border-t border-[#222] p-4">
              {/* Model selector */}
              <div className="mb-3 relative">
                <button
                  onClick={() => setIsModelSelectorOpen(!isModelSelectorOpen)}
                  className="flex items-center justify-between w-full px-3 py-2 rounded-lg bg-[#222] hover:bg-[#333] text-gray-300 transition-colors text-sm"
                >
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-6 rounded-full flex items-center justify-center overflow-hidden bg-gradient-to-r from-[#141493] to-[#417ef7]">
                      <span className="text-xs font-semibold text-white">AI</span>
                    </div>
                    <span>{t('fluxSchnellFree')}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-xs px-2 py-1 rounded-full bg-[#333] text-gray-400">{t('freeModel')}</span>
                    <FiChevronDown className={`transition-transform ${isModelSelectorOpen ? 'rotate-180' : ''}`} />
                  </div>
                </button>

                {/* Model selector dropdown */}
                <AnimatePresence>
                  {isModelSelectorOpen && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: 10 }}
                      transition={{ duration: 0.2 }}
                      className="absolute left-0 right-0 bottom-[50px] z-50 bg-[#111] border border-[#222] rounded-lg overflow-hidden shadow-lg"
                    >
                      {/* Model search */}
                      <div className="p-2 border-b border-[#222]">
                        <div className="relative">
                          <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                          <input
                            type="text"
                            value={modelSearchQuery}
                            onChange={(e) => setModelSearchQuery(e.target.value)}
                            placeholder={t('searchModels')}
                            className="w-full bg-[#181818] text-gray-300 pl-9 pr-3 py-2 rounded-md focus:outline-none focus:ring-1 focus:ring-[#417ef7]"
                          />
                        </div>
                      </div>

                      {/* Model list - only show free tier models */}
                      <div className="p-2 max-h-[350px] overflow-y-auto custom-scrollbar">
                        {FREE_TIER_MODELS
                          .filter(modelId =>
                            !modelSearchQuery ||
                            modelId.toLowerCase().includes(modelSearchQuery.toLowerCase())
                          )
                          .map(modelId => (
                            <button
                              key={modelId}
                              className={`p-3 rounded-lg text-left transition w-full ${selectedModel === modelId
                                  ? "bg-gradient-to-r from-[#141493] to-[#417ef7] text-white"
                                  : "hover:bg-[#222] text-gray-300"
                                }`}
                              onClick={() => {
                                setSelectedModel(modelId);
                                setIsModelSelectorOpen(false);
                              }}
                            >
                              <div className="flex justify-between items-center">
                                <div className="flex items-center gap-2">
                                  <div className="w-8 h-8 rounded-full flex items-center justify-center overflow-hidden bg-gradient-to-r from-[#141493] to-[#417ef7]">
                                    <span className="text-xs font-semibold text-white">AI</span>
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium">FLUX Schnell</div>
                                    <div className="text-xs text-gray-400">{t('freeImageGeneration')}</div>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  <span className="text-xs px-2 py-1 rounded-full bg-[#222] text-gray-400">{t('freeModel')}</span>
                                  <div className="w-5 h-5 rounded-full border-2 border-white flex items-center justify-center">
                                    {selectedModel === modelId && (
                                      <div className="w-3 h-3 bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full"></div>
                                    )}
                                  </div>
                                </div>
                              </div>
                            </button>
                          ))}

                        {/* Show premium models as disabled */}
                        {allModels
                          .filter(model => !FREE_TIER_MODELS.includes(model.id))
                          .slice(0, 3)
                          .filter(model =>
                            !modelSearchQuery ||
                            model.name.toLowerCase().includes(modelSearchQuery.toLowerCase())
                          )
                          .map(model => (
                            <button
                              key={model.id}
                              className="p-3 rounded-lg text-left transition w-full opacity-50 cursor-not-allowed hover:bg-[#222] text-gray-300"
                              onClick={() => {
                                toast.info(
                                  <div className="flex flex-col gap-1">
                                    <span className="font-semibold">{t('premiumModelSelected')}</span>
                                    <span className="text-sm">{t('premiumModelDescription')}</span>
                                    <Link href="/dashboard/subscription" className="text-xs text-blue-300 underline mt-1">
                                      {t('viewSubscriptionPlans')}
                                    </Link>
                                  </div>
                                );
                              }}
                            >
                              <div className="flex justify-between items-center">
                                <div className="flex items-center gap-2">
                                  <div className="w-8 h-8 rounded-full flex items-center justify-center overflow-hidden bg-gradient-to-r from-[#141493] to-[#417ef7]">
                                    <span className="text-xs font-semibold text-white">AI</span>
                                  </div>
                                  <div>
                                    <div className="text-sm font-medium">{model.name}</div>
                                    <div className="text-xs text-gray-400">{getCategoryDescription(model.category)}</div>
                                  </div>
                                </div>
                                <span className="text-xs px-2 py-1 rounded-full bg-[#222] text-gray-400">{t('premiumModel')}</span>
                              </div>
                            </button>
                          ))}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Input form */}
              <form onSubmit={(e) => {
                e.preventDefault();
                if (!prompt.trim() || isGenerating) return;
                handleGenerate();
                setPrompt('');
              }} className="relative mt-3">
                <div className="flex flex-col">
                  {/* Image preview area when image is uploaded */}
                  {uploadedImage && (
                    <div className="mb-3 bg-checkerboard rounded-lg overflow-hidden inline-block max-w-[120px] relative">
                      <div className="relative">
                        <img
                          src={uploadedImage}
                          alt="Uploaded image"
                          className="h-[80px] w-[120px] object-cover"
                        />
                        <button
                          onClick={clearUploadedImage}
                          className="absolute top-1 right-1 p-0.5 bg-black bg-opacity-70 rounded-full hover:bg-opacity-100 transition-all"
                          type="button"
                        >
                          <FiX size={12} className="text-white" />
                        </button>
                      </div>
                    </div>
                  )}

                  <div className="relative">
                    <textarea
                      className="w-full bg-[#111] border border-[#222] rounded-lg py-3 px-4 pr-12 text-gray-200 focus:outline-none focus:ring-1 focus:ring-[#417ef7] resize-none min-h-[80px] max-h-[150px]"
                      placeholder={t('prompt')}
                      value={prompt}
                      onChange={(e) => {
                        setPrompt(e.target.value);
                        // Auto-resize
                        e.target.style.height = 'inherit';
                        e.target.style.height = `${Math.min(e.target.scrollHeight, 150)}px`;
                      }}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                          e.preventDefault();
                          if (!prompt.trim() || isGenerating) return;
                          handleGenerate();
                          setPrompt('');
                          e.currentTarget.style.height = 'auto';
                        }
                      }}
                      disabled={isGenerating}
                      rows={3}
                      onPaste={handlePaste}
                    />
                    <div className="absolute right-3 bottom-3 flex items-center gap-2">
                      {/* Upload button in the prompt area - only show if model supports image input */}
                      {supportsImageInput(selectedModel) ? (
                        <label className="p-2 sm:p-1.5 rounded-lg cursor-pointer hover:bg-[#222] transition-colors min-h-[44px] sm:min-h-auto flex items-center justify-center" title={t('uploadImageTooltip')}>
                          <PaperClipIcon className="w-4 h-4 text-gray-400" />
                          <input
                            type="file"
                            accept="image/*"
                            className="hidden"
                            onChange={handleFileUpload}
                            ref={fileInputRef}
                          />
                        </label>
                      ) : (
                        <div className="p-2 sm:p-1.5 rounded-lg min-h-[44px] sm:min-h-auto flex items-center justify-center opacity-50" title={t('modelDoesNotSupportImageInput')}>
                          <PaperClipIcon className="w-4 h-4 text-gray-600" />
                        </div>
                      )}

                      {/* Send button */}
                      <button
                        type="submit"
                        className={`p-2 sm:p-1.5 rounded-lg flex-shrink-0 min-h-[44px] sm:min-h-auto flex items-center justify-center ${prompt.trim() && !isGenerating
                            ? 'bg-gradient-to-r from-[#141493] to-[#417ef7] text-white hover:opacity-90'
                            : 'bg-[#222] text-gray-500 cursor-not-allowed'
                          } transition-colors`}
                        disabled={!prompt.trim() || isGenerating}
                      >
                        <ArrowUpIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
                <div className="text-xs text-gray-500 mt-2 text-center hidden md:block">
                  {t('uploadInstructions')}
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>

      {/* Download Modal */}
      {showDownloadModal && selectedImage && (
        <DownloadModal
          isOpen={showDownloadModal}
          onClose={() => setShowDownloadModal(false)}
          selectedImage={selectedImage}
          selectedModel={selectedModel}
          allModels={allModels}
        />
      )}
    </div>
  );
};

// Define generation modes
type GenerationMode = 'image-generation' | 'image-editing' | 'upscale-image';

// Definir la interfaz para las dimensiones
interface Dimension {
  id: string;
  label: string;
  width: number;
  height: number;
}

// Dimensiones comunes
const commonDimensions: Dimension[] = [
  { id: "1024x1024", label: "1:1 Square", width: 1024, height: 1024 },
  { id: "1024x768", label: "4:3 Landscape", width: 1024, height: 768 },
  { id: "768x1024", label: "3:4 Portrait", width: 768, height: 1024 },
  { id: "1280x720", label: "16:9 Landscape", width: 1280, height: 720 },
  { id: "720x1280", label: "9:16 Portrait", width: 720, height: 1280 },
];

export default function ImageGeneration() {
  const router = useRouter();

  // Translation hooks
  const t = useImageGenerationTranslations();
  const common = useCommonTranslations();

  // Use context with hasAccess for feature-specific permission
  const {
    stripePriceId,
    subscription,
    hasAccess,
    loading: subscriptionLoading
  } = useSubscription();

  // Debug log to see what's happening
  console.log("Subscription data:", {
    stripePriceId,
    subscription,
    hasFullAccess: hasAccess('IMAGE_GENERATION')
  });



  console.log("Has subscription result:", useSubscription);

  // Default settings
  const defaultCategory = "Quick Generation";
  const defaultModelId = "black-forest-labs/FLUX.1-schnell-Free";

  // Mode selection state
  const [mode, setMode] = useState<GenerationMode>('image-generation');

  // State for the UI
  const [error, setError] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState<boolean>(false);
  const [generatedImages, setGeneratedImages] = useState<GeneratedImage[]>([]);
  const [selectedImage, setSelectedImage] = useState<GeneratedImage | null>(null);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  // Initialize messages as empty array to prevent hydration mismatch
  const [messages, setMessages] = useState<Message[]>([]);
  const [isMessagesLoaded, setIsMessagesLoaded] = useState(false);

  // Load messages from localStorage after component mounts (client-side only)
  useEffect(() => {
    try {
      const savedMessages = localStorage.getItem('astrostudio-image-generation-messages');
      if (savedMessages) {
        const serializedMessages = deserializeMessages(savedMessages);
        // Convert serialized messages back to Message objects with placeholder content
        const loadedMessages = serializedMessages.map(serialized => ({
          ...serialized,
          timestamp: new Date(serialized.timestamp),
          content: <div>Loading message content...</div> // Placeholder content
        }));
        setMessages(loadedMessages);
      }
    } catch (error) {
      console.error('Error loading messages from localStorage:', error);
    } finally {
      setIsMessagesLoaded(true);
    }
  }, []);
  const [isModelSelectorOpen, setIsModelSelectorOpen] = useState<boolean>(false);
  const [isDimensionSelectorOpen, setIsDimensionSelectorOpen] = useState<boolean>(false);
  const [modelSearchQuery, setModelSearchQuery] = useState<string>('');


  // Download modal state
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const allModels = getAllModels();

  // State for generation parameters
  const [selectedModel, setSelectedModel] = useState<string>(defaultModelId);
  const [prompt, setPrompt] = useState<string>("");
  const [negativePrompt, setNegativePrompt] = useState<string>("");
  const [numImages, setNumImages] = useState<number>(1);
  const [showAdvancedOptions, setShowAdvancedOptions] = useState<boolean>(false);
  const [activeCategory, setActiveCategory] = useState<string>(defaultCategory);

  // Dimension state
  const [dimensionType, setDimensionType] = useState<string>("preset");
  const [selectedDimension, setSelectedDimension] = useState<string>("1024x1024");
  const [customWidth, setCustomWidth] = useState<number>(1024);
  const [customHeight, setCustomHeight] = useState<number>(1024);

  // Upload related state for main component
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [uploadedImages, setUploadedImages] = useState<string[]>([]);
  const [uploadStatus, setUploadStatus] = useState<"idle" | "uploading" | "success" | "error">("idle");
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  // OpenAI API key state for GPT image model
  const [openaiApiKey ] = useState<string>(process.env.OPENAI_API_KEY ?? "");

  // Save messages to localStorage whenever they change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const serializedMessages = serializeMessages(messages);
        localStorage.setItem('astrostudio-image-generation-messages', serializedMessages);
      } catch (error) {
        console.error('Error saving messages to localStorage:', error);
      }
    }
  }, [messages]);

  // Filter to show only generation models (exclude editing models)
  const getImageGenerationCategories = () => {
    // Exclude editing-specific categories
    return Object.keys(modelsByCategory).filter(category =>
      category !== "Image Editing" &&
      category !== "Upscaling Models"
    );
  };

  // Get editing-specific categories only
  const getImageEditingCategories = () => {
    return ["Image Editing"];
  };

  // Get upscaling-specific categories only
  const getImageUpscalingCategories = () => {
    return ["Upscaling Models"];
  };

  // Handle mode change
  const handleModeChange = (newMode: GenerationMode) => {
    if (newMode === mode) return;

    // If switching to image-editing or upscale-image, navigate to the appropriate page
    if (newMode === 'image-editing') {
      router.push('/dashboard/image-editing');
      return;
    }

    if (newMode === 'upscale-image') {
      router.push('/dashboard/image-upscaling');
      return;
    }

    setMode(newMode);

    // Reset errors
    setError("");

    // Set default category and model for the new mode
    if (newMode === 'image-generation') {
      setActiveCategory("Quick Generation");
      setSelectedModel("black-forest-labs/FLUX.1-schnell-Free");
    }
  };

  // Check if a model is available for free tier
  const isModelAvailableForFreeTier = (modelId: string): boolean => {
    return FREE_TIER_MODELS.includes(modelId);
  };

  // Get the currently selected model's provider
  const getSelectedModelProvider = (): string => {
    for (const category of Object.keys(modelsByCategory)) {
      const model = modelsByCategory[category].find(
        (m) => m.id === selectedModel,
      );
      if (model) {
        return model.provider;
      }
    }

    // Default provider detection based on model ID
    if (selectedModel.includes('together-ai') || selectedModel.startsWith('black-forest-labs/FLUX')) {
      return "Together AI";
    }

    return "Replicate"; // Default provider
  };

  // Handle model change with subscription check
  const handleModelChange = (modelId: string) => {
    // Simply set the selected model without additional filtering
    // The API already checks permissions based on the user's subscription
    setSelectedModel(modelId);

    // Show a notification for premium models if user is not subscribed
    if (!subscription && !isModelAvailableForFreeTier(modelId)) {
      toast.info(
        <div className="flex flex-col gap-1">
          <span className="font-semibold">{t('premiumModelSelected')}</span>
          <span className="text-sm">{t('premiumModelDescription')}</span>
          <Link href="/dashboard/subscription" className="text-xs text-purple-300 underline mt-1">
            {t('viewSubscriptionPlans')}
          </Link>
        </div>
      );
    }
  };

  // Get width and height values
  const getDimensions = () => {
    if (dimensionType === "custom") {
      return { width: customWidth, height: customHeight };
    } else {
      const [width, height] = selectedDimension.split("x").map(Number);
      return { width, height };
    }
  };

  // Handle dimension change
  const handleDimensionChange = (value: string) => {
    if (value === "custom") {
      setDimensionType("custom");
    } else {
      setDimensionType("preset");
      setSelectedDimension(value);
    }
  };

  // Handle image generation
  const handleGenerate = async () => {
    if (!prompt.trim() && !uploadedImage && uploadedImages.length === 0) {
      toast.error(t('errors.promptRequired'));
      return;
    }

    try {
      setError("");
      setIsGenerating(true);

      const { width, height } = getDimensions();
      const provider = getSelectedModelProvider();
      const isReplicateModel = provider === "Replicate";

      // Add user message
      setMessages(prev => [
        ...prev,
        createTextMessage(
          prompt,
          true,
          undefined,
          selectedModel,
          uploadedImage || undefined,
          uploadedImages.length > 0 ? uploadedImages : undefined
        )
      ]);

      // Prepare request data
      const requestData: any = {
        model: selectedModel,
        provider,
        prompt: prompt.trim(),
        negative_prompt: negativePrompt.trim(),
        width,
        height,
        num_images: numImages,
        feature: "IMAGE_GENERATION",
        image: uploadedImage ?? undefined
      };

      // Add input_images for models that support image input
      if (supportsImageInput(selectedModel)) {
        if (uploadedImages.length > 0) {
          requestData.input_images = uploadedImages;
        } else if (uploadedImage) {
          requestData.input_images = [uploadedImage];
        }
      }
      

      // Make API request
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error ?? 'Failed to generate image');
      }

      const data = await response.json();

      // Enhanced Replicate response handling
      let finalImages: string[] = [];
      if (isReplicateModel) {
        // First try the standard images array
        if (data.images && Array.isArray(data.images) && data.images.length > 0) {
          finalImages = data.images.filter((url: any) => typeof url === 'string' && url.trim().length > 0);
        }

        // If no images found, try to extract from raw_response
        if (finalImages.length === 0 && data.raw_response) {
          const rawResponse = data.raw_response;

          // Try different possible paths in the raw response
          const possiblePaths = ['output', 'images', 'data', 'urls', 'image_urls'];
          for (const path of possiblePaths) {
            if (rawResponse[path]) {
              if (Array.isArray(rawResponse[path])) {
                const validUrls = rawResponse[path].filter((url: any) => typeof url === 'string' && url.trim().length > 0);
                if (validUrls.length > 0) {
                  finalImages = validUrls;
                  break;
                }
              } else if (typeof rawResponse[path] === 'string' && rawResponse[path].trim().length > 0) {
                finalImages = [rawResponse[path]];
                break;
              }
            }
          }
        }

        // Update data.images with the extracted images
        data.images = finalImages;
      } else {
        // For non-Replicate providers, use images as-is
        finalImages = data.images ?? [];
      }

      // Create new generated images
      const newImages: GeneratedImage[] = finalImages.map((url: string, index: number) => ({
        id: Date.now() + index,
        url,
        prompt,
        negativePrompt,
        model: selectedModel,
        width,
        height,
        timestamp: new Date(),
        provider,
        raw_response: isReplicateModel ? data.raw_response : null
      }));

      if (newImages.length === 0) {
        throw new Error(t('errors.noImagesGenerated'));
      }

      setGeneratedImages(prev => [...newImages, ...prev]);
      setSelectedImage(newImages[0]);

      // Add AI message with generated images
      setMessages(prev => [
        ...prev,
        createImageGridMessage(
          newImages,
          provider,
          selectedModel,
          numImages,
          false
        )
      ]);

      // Clear prompt and upload
      setPrompt("");
      setUploadedImage(null);
      setUploadedImages([]);
      setUploadStatus("idle");
    } catch (err) {
      if (err instanceof Error) {
        setError(err.message || t('errors.generationFailed'));
        toast.error(err.message || t('errors.generationFailed'));
      } else {
        setError(t('errors.generationFailed'));
        toast.error(t('errors.generationFailed'));
      }
    } finally {
      setIsGenerating(false);
    }
  };

  // Save generated image to Supabase
  const saveImageToSupabase = async (imageUrl: string, index: number): Promise<string | null> => {
    try {
      const response = await fetch('/api/user/save-generated-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          imageUrl,
          prompt,
          model: selectedModel,
          width: getDimensions().width,
          height: getDimensions().height,
        }),
      });

      if (!response.ok) {
        throw new Error(t('errors.failedToSaveImage'));
      }

      // Return the image URL on success
      return imageUrl;
    } catch (error) {
      console.error('Error saving image to user history:', error);
      return null;
    }
  };

  // Copy prompt to clipboard
  const copyPrompt = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(t('success.promptCopied'));
    } catch (err) {
      console.error('Failed to copy text:', err);
      setError(t('errors.failedToCopyText'));
    }
  };

  // Download image
  const downloadImage = async (url: string) => {
    // If the image is selected, open the download modal instead of direct download
    if (selectedImage && selectedImage.url === url) {
      setShowDownloadModal(true);
      return;
    }

    // Find the image in generatedImages if not the selected one
    const imageToDownload = generatedImages.find(img => img.url === url);
    if (imageToDownload) {
      setSelectedImage(imageToDownload);
      setShowDownloadModal(true);
      return;
    }

    // Fallback to direct download if no image found
    try {
      // Use the download-image API to handle CORS issues
      const response = await fetch('/api/download-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ imageUrl: url }),
      });

      if (!response.ok) {
        throw new Error(t('errors.failedToFetchImageData'));
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error ?? t('errors.failedToDownloadImage'));
      }

      // Create a temporary link to download the data URL
      const a = document.createElement('a');
      a.href = data.dataUrl;
      a.download = `generated-image-${Date.now()}.png`;
      document.body.appendChild(a);
      a.click();

      // Clean up
      document.body.removeChild(a);
    } catch (err) {
      console.error('Error downloading image:', err);
      toast.error(t('errors.failedToDownloadImage'));
    }
  };

  // Create preview content for selected image in this scope
  const generationPreviewContent = selectedImage ? (
    <div className="bg-[#111] rounded-xl overflow-hidden mb-4">
      <img
        src={selectedImage.url}
        alt={selectedImage.prompt}
        className="max-h-[400px] mx-auto object-contain"
      />
      <div className="flex justify-end p-3 gap-2">
        <button
          onClick={() => selectedImage.prompt && copyPrompt(selectedImage.prompt)}
          className="p-1.5 rounded-md bg-[#222] hover:bg-[#333] text-gray-300 transition-colors text-xs flex items-center gap-1"
          title={t('copyPrompt')}
        >
          <FiCopy size={14} /> {t('copyPrompt')}
        </button>
        <button
          onClick={() => downloadImage(selectedImage.url)}
          className="p-1.5 rounded-md bg-[#222] hover:bg-[#333] text-gray-300 transition-colors text-xs flex items-center gap-1"
          title={t('downloadImage')}
        >
          <FiDownload size={14} /> {t('downloadImage')}
        </button>
      </div>
    </div>
  ) : null;

  // Handle file upload for image-to-image generation
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Check if the selected model supports image input
    if (!supportsImageInput(selectedModel)) {
      toast.error(t('errors.modelNotSupported'));
      return;
    }
    
    if (!file.type.startsWith("image/")) {
      toast.error(t('errors.imageFilesOnly'));
      return;
    }
    if (file.size > 10 * 1024 * 1024) {
      toast.error(t('errors.fileSizeLimit'));
      return;
    }
    setUploadStatus("uploading");
    toast.loading(t('loading.uploadingImage'));
    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target?.result) {
        const result = event.target.result as string;
        setUploadedImage(result);
        setUploadStatus("success");
        toast.dismiss();
        toast.success(`1 ${t('success.imageUploaded')}`);
      }
    };
    reader.onerror = () => {
      setUploadStatus("error");
      setError(t('errors.failedToReadImageFile'));
      toast.dismiss();
      toast.error(t('errors.failedToReadImageFile'));
    };
    reader.readAsDataURL(file);
  };

  // Clear uploaded image
  const clearUploadedImage = () => {
    setUploadedImage(null);
    setUploadStatus("idle");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Handle paste for image upload
  const handlePaste = (e: React.ClipboardEvent) => {
    const items = e.clipboardData.items;
    for (let i = 0; i < items.length; i++) {
      if (items[i].type.indexOf('image') !== -1) {
        const blob = items[i].getAsFile();
        if (!blob) continue;

        // Check file size
        if (blob.size > 10 * 1024 * 1024) {
          toast.error(t('errors.fileSizeLimit'));
          return;
        }

        setUploadStatus("uploading");
        toast.loading(t('loading.processingPastedImage'));

        const reader = new FileReader();
        reader.onload = (event) => {
          if (event.target?.result) {
            const result = event.target.result as string;
            setUploadedImage(result);
            setUploadStatus("success");
            setError("");
            toast.dismiss();
            toast.success(t('pasteImageSuccess'));

            // Add a message about the uploaded image
            setMessages(prev => [
              ...prev,
              createImageUploadMessage(
                t('pasteImageDescription'),
                result,
                true
              )
            ]);
          }
        };

        reader.onerror = () => {
          setUploadStatus("error");
          setError(t('errors.failedToReadImageFile'));
          toast.dismiss();
          toast.error(t('errors.failedToReadImageFile'));
        };

        reader.readAsDataURL(blob);
        break;
      }
    }
  };

  // Render content based on mode
  const renderContent = () => {
    if (mode === 'upscale-image') {
      router.push('/dashboard/image-editing?mode=upscale');
      return null;
    }

    return (
      <div className="h-full flex flex-col">
        {/* Chat Interface with dark styling */}
        <div className="flex-1 min-h-0 overflow-hidden">
          <div className="h-full flex flex-col bg-black rounded-xl">
            <div className="flex items-center justify-between p-4 border-b border-[#222]">
              <h1 className="text-xl font-medium text-white">{t('mainTitle')}</h1>
              <div className="flex items-center gap-2">
                <button
                  className="flex items-center text-gray-400 hover:text-white text-sm bg-[#222] hover:bg-[#333] px-3 py-1.5 rounded-lg"
                  onClick={() => setShowAdvancedOptions(!showAdvancedOptions)}
                >
                  <FiGrid className="mr-2" />
                  {showAdvancedOptions ? "Hide" : "Show"} Dimensions
                </button>
              </div>
            </div>

            {/* Settings Panel (conditionally shown) */}
            {showAdvancedOptions && (
              <div className="bg-[#111] border-b border-[#222] p-4">
                <div className="flex flex-wrap gap-2 justify-between items-center">
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-400">Size:</span>
                    <div className="relative">
                      <button
                        onClick={() => setIsDimensionSelectorOpen(!isDimensionSelectorOpen)}
                        className="flex items-center justify-between px-3 py-1.5 rounded-lg bg-[#222] hover:bg-[#333] text-gray-300 transition-colors text-sm"
                      >
                        <span>
                          {commonDimensions.find(d => d.id === selectedDimension)?.label ?? selectedDimension}
                        </span>
                        <FiChevronDown className={`ml-2 transition-transform ${isDimensionSelectorOpen ? 'rotate-180' : ''}`} />
                      </button>

                      {/* Dimension selector dropdown */}
                      <AnimatePresence>
                        {isDimensionSelectorOpen && (
                          <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            exit={{ opacity: 0, y: 10 }}
                            transition={{ duration: 0.2 }}
                            className="absolute left-0 top-full mt-1 z-50 bg-[#111] border border-[#222] rounded-lg overflow-hidden shadow-lg"
                          >
                            <div className="p-2 max-h-[350px] overflow-y-auto custom-scrollbar">
                              {commonDimensions.map((dimension) => (
                                <button
                                  key={dimension.id}
                                  className={`p-3 rounded-lg text-left transition w-full ${selectedDimension === dimension.id
                                      ? "bg-gradient-to-r from-[#141493] to-[#417ef7] text-white"
                                      : "hover:bg-[#222] text-gray-300"
                                    }`}
                                  onClick={() => {
                                    setSelectedDimension(dimension.id);
                                    setIsDimensionSelectorOpen(false);
                                  }}
                                >
                                  <div className="flex justify-between items-center">
                                    <div>
                                      <div className="text-sm font-medium">{dimension.label}</div>
                                      <div className="text-xs text-gray-400">{dimension.width} x {dimension.height}</div>
                                    </div>
                                    <div className="w-5 h-5 rounded-full border-2 border-white flex items-center justify-center">
                                      {selectedDimension === dimension.id && (
                                        <div className="w-3 h-3 bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full"></div>
                                      )}
                                    </div>
                                  </div>
                                </button>
                              ))}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Chat Interface */}
            <div className="flex flex-col h-full overflow-y-auto">
              {/* Message area */}
              <div className="flex-1 p-3 overflow-y-auto auto-scroll custom-scrollbar">
                {messages.length === 0 && (
                  <div className="flex flex-col items-center justify-center h-full text-center text-gray-400 space-y-6 py-10">
                    <div className="flex flex-col items-center">
                      <Image src="/Logo Web.webp" alt="AstroStudio AI" width={200} height={70} className="mb-4" />
                      <p className="text-2xl font-medium text-gray-300 mb-4">{t('aiImageGeneration')}</p>
                      <p className="max-w-lg mx-auto">
                        {t('description')}
                      </p>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 w-full max-w-2xl">
                      <button
                        className="bg-[#111] hover:bg-[#222] rounded-xl p-4 text-left transition-colors"
                        onClick={() => {
                          setPrompt("A photorealistic portrait of a young woman with blue eyes and blonde hair, detailed facial features, studio lighting");
                        }}
                      >
                        <p className="font-medium mb-1">{t('examples.photoRealistic.title')}</p>
                        <p className="text-sm text-gray-500">{t('examples.photoRealistic.description')}</p>
                      </button>
                      <button
                        className="bg-[#111] hover:bg-[#222] rounded-xl p-4 text-left transition-colors"
                        onClick={() => {
                          setPrompt("A fantasy landscape with mountains, waterfalls, and a castle on a cliff, magical atmosphere, dramatic lighting");
                        }}
                      >
                        <p className="font-medium mb-1">{t('examples.fantasyLandscape.title')}</p>
                        <p className="text-sm text-gray-500">{t('examples.fantasyLandscape.description')}</p>
                      </button>
                    </div>
                  </div>
                )}

                {/* Messages */}
                <div className="space-y-6">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex ${message.isUser ? 'justify-end' : 'justify-start'} max-w-full`}
                    >
                      <div className={`flex ${message.isUser ? 'flex-row-reverse' : 'flex-row'} items-start gap-3 max-w-[90%]`}>
                        {/* Message content */}
                        <div
                          className={`${message.isUser
                              ? 'message-bubble-user'
                              : 'message-bubble-ai'
                            } max-w-full overflow-hidden`}
                        >
                          <div className="break-words">
                            <MessageRenderer
                              message={message}
                              onSelectImage={setSelectedImage}
                              onDownloadImage={downloadImage}
                              onSaveImage={saveImageToSupabase}
                              isSaving={isSaving}
                              ReplicateImagesGrid={ReplicateImagesGrid}
                              GeneratedImagesGrid={GeneratedImagesGrid}
                            />
                          </div>

                          {/* If this is an AI message with images, add download buttons */}
                          {!message.isUser && message.model && message.content && typeof message.content !== 'string' && (
                            <div className="mt-2 flex justify-end gap-2">
                              <button
                                onClick={() => downloadImage(selectedImage?.url ?? '')}
                                className="p-1 rounded-md bg-[#222] hover:bg-[#333] text-gray-300 transition-colors text-xs flex items-center gap-1"
                              >
                                <FiDownload size={14} /> Download
                              </button>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* Loading indicator */}
                  {isGenerating && (
                    <div className="flex justify-start max-w-full">
                      <div className="flex flex-row items-start gap-3 max-w-[90%]">
                        <div className="message-bubble-ai">
                          <div className="flex space-x-2 items-center h-5">
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse"></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                            <div className="w-2 h-2 bg-gray-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Input area with model selector */}
              <div className="border-t border-[#222] p-4">
                {/* Model selector */}
                <div className="relative">
                  <button
                    onClick={() => setIsModelSelectorOpen(!isModelSelectorOpen)}
                    className="flex items-center justify-between w-full px-3 py-2 rounded-lg bg-[#111] hover:bg-[#181818] transition-colors mb-3"
                  >
                    <div className="flex items-center gap-2">
                      <div className="w-6 h-6 rounded-full flex items-center justify-center overflow-hidden bg-gradient-to-r from-[#141493] to-[#417ef7]">
                        <span className="text-xs font-semibold text-white">
                          AI
                        </span>
                      </div>
                      <span className="text-sm text-gray-200">
                        {(() => {
                          for (const category of Object.keys(modelsByCategory)) {
                            const model = modelsByCategory[category].find(m => m.id === selectedModel);
                            if (model) return model.name;
                          }
                          return t('selectModel');
                        })()}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <FiChevronDown className={`text-gray-400 transition-transform ${isModelSelectorOpen ? 'rotate-180' : ''}`} />
                    </div>
                  </button>

                  {/* Model selector dropdown */}
                  <AnimatePresence>
                    {isModelSelectorOpen && (
                      <motion.div
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: 10 }}
                        transition={{ duration: 0.2 }}
                        className="absolute left-0 right-0 bottom-[50px] z-50 bg-[#111] border border-[#222] rounded-lg overflow-hidden shadow-lg"
                      >
                        {/* Model search */}
                        <div className="p-2 border-b border-[#222]">
                          <div className="relative">
                            <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                            <input
                              type="text"
                              value={modelSearchQuery}
                              onChange={(e) => setModelSearchQuery(e.target.value)}
                              placeholder={t('searchModels')}
                              className="w-full bg-[#181818] text-gray-300 pl-9 pr-3 py-2 rounded-md focus:outline-none focus:ring-1 focus:ring-[#417ef7]"
                            />
                          </div>
                        </div>

                        {/* Model list - show all models in a flat list */}
                        <div className="p-2 max-h-[350px] overflow-y-auto custom-scrollbar">
                          {Object.keys(modelsByCategory)
                            .filter(category =>
                              category !== "Image Editing" &&
                              category !== "Upscaling Models"
                            )
                            .flatMap(category =>
                              modelsByCategory[category].map(model => ({
                                ...model,
                                category
                              }))
                            )
                            .filter(model =>
                              !modelSearchQuery ||
                              model.name.toLowerCase().includes(modelSearchQuery.toLowerCase()) ||
                              model.provider.toLowerCase().includes(modelSearchQuery.toLowerCase()) ||
                              getCategoryDescription(model.category).toLowerCase().includes(modelSearchQuery.toLowerCase())
                            )
                            .map(model => (
                              <button
                                key={model.id}
                                className={`p-3 rounded-lg text-left transition w-full ${selectedModel === model.id
                                    ? "bg-gradient-to-r from-[#141493] to-[#417ef7] text-white"
                                    : "hover:bg-[#222] text-gray-300"
                                  } ${!subscription && !FREE_TIER_MODELS.includes(model.id) ? 'opacity-50' : ''}`}
                                onClick={() => {
                                  if (!subscription && !FREE_TIER_MODELS.includes(model.id)) {
                                    toast.info(
                                      <div className="flex flex-col gap-1">
                                        <span className="font-semibold">{t('premiumModelSelected')}</span>
                                        <span className="text-sm">{t('premiumModelDescription')}</span>
                                        <Link href="/dashboard/subscription" className="text-xs text-blue-300 underline mt-1">
                                          {t('viewSubscriptionPlans')}
                                        </Link>
                                      </div>
                                    );
                                    return;
                                  }
                                  setSelectedModel(model.id);
                                  setIsModelSelectorOpen(false);
                                }}
                              >
                                <div className="flex justify-between items-center">
                                  <div className="flex items-center gap-2">
                                    <div className="w-8 h-8 rounded-full flex items-center justify-center overflow-hidden bg-gradient-to-r from-[#141493] to-[#417ef7]">
                                      <span className="text-xs font-semibold text-white">
                                        AI
                                      </span>
                                    </div>
                                    <div>
                                      <div className="text-sm font-medium">{model.name}</div>
                                      <div className="text-xs text-gray-400">{getCategoryDescription(model.category)}</div>
                                    </div>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    {!subscription && !FREE_TIER_MODELS.includes(model.id) && (
                                      <span className="text-xs px-2 py-1 rounded-full bg-[#222] text-gray-400">Premium</span>
                                    )}
                                    <div className="w-5 h-5 rounded-full border-2 border-white flex items-center justify-center">
                                      {selectedModel === model.id && (
                                        <div className="w-3 h-3 bg-gradient-to-r from-[#141493] to-[#417ef7] rounded-full"></div>
                                      )}
                                    </div>
                                  </div>
                                </div>
                              </button>
                            ))}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>

                {/* Input form */}
                <form onSubmit={(e) => {
                  e.preventDefault();

                  if (!prompt.trim() || isGenerating) return;

                  handleGenerate();
                  setPrompt('');
                }} className="relative mt-3">
                  <div className="flex flex-col">
                    {/* Image preview area when image is uploaded */}
                    {uploadedImage && (
                      <div className="mb-3 bg-checkerboard rounded-lg overflow-hidden inline-block max-w-[120px] relative">
                        <div className="relative">
                          <img
                            src={uploadedImage}
                            alt="Uploaded image"
                            className="h-[80px] w-[120px] object-cover"
                          />
                          <button
                            onClick={clearUploadedImage}
                            className="absolute top-1 right-1 p-0.5 bg-black bg-opacity-70 rounded-full hover:bg-opacity-100 transition-all"
                            type="button"
                          >
                            <FiX size={12} className="text-white" />
                          </button>
                        </div>
                      </div>
                    )}

                    <div className="relative">
                      <textarea
                        className="w-full bg-[#111] border border-[#222] rounded-lg py-3 px-4 pr-12 text-gray-200 focus:outline-none focus:ring-1 focus:ring-[#417ef7] resize-none min-h-[80px] max-h-[150px]"
                        placeholder={t('placeholder.imageGeneration')}
                        value={prompt}
                        onChange={(e) => {
                          setPrompt(e.target.value);
                          // Auto-resize
                          e.target.style.height = 'inherit';
                          e.target.style.height = `${Math.min(e.target.scrollHeight, 150)}px`;
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            if (!prompt.trim() || isGenerating) return;
                            handleGenerate();
                            setPrompt('');
                            e.currentTarget.style.height = 'auto';
                          }
                        }}
                        disabled={isGenerating}
                        rows={3}
                        onPaste={handlePaste}
                      />
                      <div className="absolute right-3 bottom-3 flex items-center gap-2">
                        {/* Upload button in the prompt area - only show if model supports image input */}
                        {supportsImageInput(selectedModel) ? (
                          <label className="p-1.5 rounded-lg cursor-pointer hover:bg-[#222] transition-colors" title={t('uploadImageTooltip')}>
                            <PaperClipIcon className="w-4 h-4 text-gray-400" />
                            <input
                              type="file"
                              accept="image/*"
                              className="hidden"
                              onChange={handleFileUpload}
                              ref={fileInputRef}
                            />
                          </label>
                        ) : (
                          <div className="p-1.5 rounded-lg opacity-50" title={t('modelDoesNotSupportImageInput')}>
                            <PaperClipIcon className="w-4 h-4 text-gray-600" />
                          </div>
                        )}

                        {/* Send button */}
                        <button
                          type="submit"
                          className={`p-1.5 rounded-lg flex-shrink-0 ${prompt.trim() && !isGenerating
                              ? 'bg-gradient-to-r from-[#141493] to-[#417ef7] text-white hover:opacity-90'
                              : 'bg-[#222] text-gray-500 cursor-not-allowed'
                            } transition-colors`}
                          disabled={!prompt.trim() || isGenerating}
                        >
                          <ArrowUpIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                  <div className="text-xs text-gray-500 mt-2 text-center hidden md:block">
                    {t('uploadInstructions')}
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        {/* Download Modal */}
        {showDownloadModal && selectedImage && (
          <DownloadModal
            isOpen={showDownloadModal}
            onClose={() => setShowDownloadModal(false)}
            selectedImage={selectedImage}
            selectedModel={selectedModel}
            allModels={allModels}
          />
        )}
      </div>
    );
  };

  // Render content based on mode
  return (
    <>
      <style jsx global>{customStyles}</style>

      {!subscription ? (
        <FreeTierContent />
      ) : (
        renderContent()
      )}
    </>
  );
}
